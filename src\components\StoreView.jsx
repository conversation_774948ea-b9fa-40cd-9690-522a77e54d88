import React, { Suspense, useState, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Environment, Html } from '@react-three/drei'
import ShoeShelf from './3D/ShoeShelf'
import LoadingSpinner from './LoadingSpinner'

const StoreView = () => {
  const [shoes, setShoes] = useState([])
  const [loading, setLoading] = useState(true)

  // بيانات تجريبية للكوتشيات
  useEffect(() => {
    // محاكاة تحميل البيانات من API
    setTimeout(() => {
      setShoes([
        {
          id: 1,
          name: 'Nike Air Max 270',
          price: 1200,
          colors: ['أسود', 'أبيض', 'أحمر'],
          sizes: [40, 41, 42, 43, 44],
          position: [-2, 1, 0],
          image: '/images/nike-air-max.jpg'
        },
        {
          id: 2,
          name: 'Adidas Ultraboost',
          price: 1500,
          colors: ['أزرق', 'رمادي', 'أخضر'],
          sizes: [39, 40, 41, 42, 43],
          position: [0, 1, 0],
          image: '/images/adidas-ultraboost.jpg'
        },
        {
          id: 3,
          name: 'Puma RS-X',
          price: 900,
          colors: ['أصفر', 'بنفسجي', 'وردي'],
          sizes: [38, 39, 40, 41, 42],
          position: [2, 1, 0],
          image: '/images/puma-rsx.jpg'
        },
        {
          id: 4,
          name: 'Jordan 1 Retro',
          price: 2000,
          colors: ['أسود وأحمر', 'أبيض وأزرق'],
          sizes: [40, 41, 42, 43, 44, 45],
          position: [-2, -0.5, 0],
          image: '/images/jordan-1.jpg'
        },
        {
          id: 5,
          name: 'Converse Chuck Taylor',
          price: 600,
          colors: ['أسود', 'أبيض', 'أحمر', 'أزرق'],
          sizes: [37, 38, 39, 40, 41, 42],
          position: [0, -0.5, 0],
          image: '/images/converse-chuck.jpg'
        },
        {
          id: 6,
          name: 'Vans Old Skool',
          price: 800,
          colors: ['أسود وأبيض', 'أزرق داكن'],
          sizes: [38, 39, 40, 41, 42, 43],
          position: [2, -0.5, 0],
          image: '/images/vans-oldskool.jpg'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  if (loading) {
    return <LoadingSpinner />
  }

  return (
    <div className="hero-section">
      {/* Hero Section */}
      <div className="hero-content">
        <div className="container">
          <div className="row align-items-center min-vh-100">
            <div className="col-lg-6">
              <div className="hero-text">
                <h1 className="display-3 fw-bold text-white mb-4">
                  أهلاً بك في متجرنا
                  <span className="text-warning d-block">FiftyFive</span>
                </h1>
                <p className="lead text-white-50 mb-4">
                  اكتشف أحدث تشكيلة من الكوتشيات العالمية في متجرنا الافتراضي ثلاثي الأبعاد
                </p>
                <div className="d-flex gap-3 mb-4">
                  <button className="btn btn-warning btn-lg px-4 py-3 fw-bold">
                    <i className="fas fa-shopping-bag me-2"></i>
                    تسوق الآن
                  </button>
                  <button className="btn btn-outline-light btn-lg px-4 py-3">
                    <i className="fas fa-play me-2"></i>
                    جولة افتراضية
                  </button>
                </div>
                <div className="hero-stats d-flex gap-4">
                  <div className="stat-item text-center">
                    <h3 className="text-warning fw-bold">500+</h3>
                    <p className="text-white-50 small">منتج متاح</p>
                  </div>
                  <div className="stat-item text-center">
                    <h3 className="text-warning fw-bold">50+</h3>
                    <p className="text-white-50 small">ماركة عالمية</p>
                  </div>
                  <div className="stat-item text-center">
                    <h3 className="text-warning fw-bold">24/7</h3>
                    <p className="text-white-50 small">خدمة العملاء</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="hero-3d-container">
                <div className="canvas-container" style={{ height: '600px', borderRadius: '20px', overflow: 'hidden' }}>
                  <Canvas
                    camera={{ position: [0, 2, 8], fov: 60 }}
                    shadows
                  >
                    <Suspense fallback={
                      <Html center>
                        <div className="loading-spinner">
                          <div className="spinner-border text-warning" role="status">
                            <span className="visually-hidden">جاري التحميل...</span>
                          </div>
                        </div>
                      </Html>
                    }>
                      <Environment preset="warehouse" />
                      <ambientLight intensity={0.4} />
                      <directionalLight
                        position={[10, 10, 5]}
                        intensity={1}
                        castShadow
                      />

                      <ShoeShelf shoes={shoes} />

                      <OrbitControls
                        enablePan={true}
                        enableZoom={true}
                        enableRotate={true}
                        minDistance={3}
                        maxDistance={15}
                        autoRotate={true}
                        autoRotateSpeed={1}
                      />
                    </Suspense>
                  </Canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="features-section py-5">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center mb-5">
              <h2 className="display-5 fw-bold text-primary mb-3">لماذا FiftyFive؟</h2>
              <p className="lead text-muted">تجربة تسوق فريدة بتقنية ثلاثية الأبعاد</p>
            </div>
          </div>
          <div className="row g-4">
            <div className="col-md-4">
              <div className="feature-card text-center p-4 h-100">
                <div className="feature-icon mb-3">
                  <i className="fas fa-cube fa-3x text-primary"></i>
                </div>
                <h4 className="fw-bold mb-3">تجربة 3D تفاعلية</h4>
                <p className="text-muted">استكشف المنتجات من جميع الزوايا في بيئة ثلاثية الأبعاد</p>
              </div>
            </div>
            <div className="col-md-4">
              <div className="feature-card text-center p-4 h-100">
                <div className="feature-icon mb-3">
                  <i className="fas fa-shipping-fast fa-3x text-success"></i>
                </div>
                <h4 className="fw-bold mb-3">شحن سريع</h4>
                <p className="text-muted">توصيل مجاني لجميع أنحاء المملكة خلال 24 ساعة</p>
              </div>
            </div>
            <div className="col-md-4">
              <div className="feature-card text-center p-4 h-100">
                <div className="feature-icon mb-3">
                  <i className="fas fa-shield-alt fa-3x text-warning"></i>
                </div>
                <h4 className="fw-bold mb-3">ضمان الجودة</h4>
                <p className="text-muted">جميع منتجاتنا أصلية 100% مع ضمان الاستبدال</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Instructions */}
      <div className="instructions-section py-4 bg-light">
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="alert alert-info text-center border-0 shadow-sm">
                <i className="fas fa-mouse-pointer me-2 text-primary"></i>
                <strong>كيفية الاستخدام:</strong> استخدم الماوس للتحرك حول المتجر الافتراضي واضغط على أي كوتشي لرؤية التفاصيل والألوان المتاحة
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
            
            <div className="canvas-container" style={{ height: '600px' }}>
              <Canvas
                camera={{ position: [0, 2, 8], fov: 60 }}
                shadows
              >
                <Suspense fallback={
                  <Html center>
                    <div className="loading-spinner">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">جاري التحميل...</span>
                      </div>
                    </div>
                  </Html>
                }>
                  <Environment preset="warehouse" />
                  <ambientLight intensity={0.4} />
                  <directionalLight 
                    position={[10, 10, 5]} 
                    intensity={1} 
                    castShadow 
                  />
                  
                  <ShoeShelf shoes={shoes} />
                  
                  <OrbitControls 
                    enablePan={true}
                    enableZoom={true}
                    enableRotate={true}
                    minDistance={3}
                    maxDistance={15}
                  />
                </Suspense>
              </Canvas>
            </div>
            
            <div className="row mt-4">
              <div className="col-12">
                <div className="alert alert-info text-center">
                  <i className="fas fa-mouse-pointer me-2"></i>
                  استخدم الماوس للتحرك حول المتجر واضغط على أي كوتشي لرؤية التفاصيل
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StoreView
