{"version": 3, "file": "BasisTextureLoader.cjs", "sources": ["../../src/loaders/BasisTextureLoader.js"], "sourcesContent": ["import {\n  CompressedTexture,\n  FileLoader,\n  LinearFilter,\n  LinearMipmapLinearFilter,\n  Loader,\n  RGBAFormat,\n  RGBA_ASTC_4x4_Format,\n  RGBA_BPTC_Format,\n  RGBA_ETC2_EAC_Format,\n  RGBA_PVRTC_4BPPV1_Format,\n  RGBA_S3TC_DXT5_Format,\n  RGB_ETC1_Format,\n  RGB_ETC2_Format,\n  RGB_PVRTC_4BPPV1_Format,\n  RGB_S3TC_DXT1_Format,\n  UnsignedByteType,\n} from 'three'\n\n/**\n * Loader for Basis Universal GPU Texture Codec.\n *\n * Basis Universal is a \"supercompressed\" GPU texture and texture video\n * compression system that outputs a highly compressed intermediate file format\n * (.basis) that can be quickly transcoded to a wide variety of GPU texture\n * compression formats.\n *\n * This loader parallelizes the transcoding process across a configurable number\n * of web workers, before transferring the transcoded compressed texture back\n * to the main thread.\n */\n\nconst _taskCache = new WeakMap()\n\nconst BasisTextureLoader = /* @__PURE__ */ (() => {\n  class BasisTextureLoader extends Loader {\n    /* CONSTANTS */\n\n    static BasisFormat = {\n      ETC1S: 0,\n      UASTC_4x4: 1,\n    }\n\n    static TranscoderFormat = {\n      ETC1: 0,\n      ETC2: 1,\n      BC1: 2,\n      BC3: 3,\n      BC4: 4,\n      BC5: 5,\n      BC7_M6_OPAQUE_ONLY: 6,\n      BC7_M5: 7,\n      PVRTC1_4_RGB: 8,\n      PVRTC1_4_RGBA: 9,\n      ASTC_4x4: 10,\n      ATC_RGB: 11,\n      ATC_RGBA_INTERPOLATED_ALPHA: 12,\n      RGBA32: 13,\n      RGB565: 14,\n      BGR565: 15,\n      RGBA4444: 16,\n    }\n\n    static EngineFormat = {\n      RGBAFormat: RGBAFormat,\n      RGBA_ASTC_4x4_Format: RGBA_ASTC_4x4_Format,\n      RGBA_BPTC_Format: RGBA_BPTC_Format,\n      RGBA_ETC2_EAC_Format: RGBA_ETC2_EAC_Format,\n      RGBA_PVRTC_4BPPV1_Format: RGBA_PVRTC_4BPPV1_Format,\n      RGBA_S3TC_DXT5_Format: RGBA_S3TC_DXT5_Format,\n      RGB_ETC1_Format: RGB_ETC1_Format,\n      RGB_ETC2_Format: RGB_ETC2_Format,\n      RGB_PVRTC_4BPPV1_Format: RGB_PVRTC_4BPPV1_Format,\n      RGB_S3TC_DXT1_Format: RGB_S3TC_DXT1_Format,\n    }\n\n    /* WEB WORKER */\n\n    static BasisWorker = function () {\n      let config\n      let transcoderPending\n      let BasisModule\n\n      const EngineFormat = _EngineFormat\n      const TranscoderFormat = _TranscoderFormat\n      const BasisFormat = _BasisFormat\n\n      onmessage = function (e) {\n        const message = e.data\n\n        switch (message.type) {\n          case 'init':\n            config = message.config\n            init(message.transcoderBinary)\n            break\n\n          case 'transcode':\n            transcoderPending.then(() => {\n              try {\n                const { width, height, hasAlpha, mipmaps, format } = message.taskConfig.lowLevel\n                  ? transcodeLowLevel(message.taskConfig)\n                  : transcode(message.buffers[0])\n\n                const buffers = []\n\n                for (let i = 0; i < mipmaps.length; ++i) {\n                  buffers.push(mipmaps[i].data.buffer)\n                }\n\n                self.postMessage(\n                  { type: 'transcode', id: message.id, width, height, hasAlpha, mipmaps, format },\n                  buffers,\n                )\n              } catch (error) {\n                console.error(error)\n\n                self.postMessage({ type: 'error', id: message.id, error: error.message })\n              }\n            })\n            break\n        }\n      }\n\n      function init(wasmBinary) {\n        transcoderPending = new Promise((resolve) => {\n          BasisModule = { wasmBinary, onRuntimeInitialized: resolve }\n          BASIS(BasisModule)\n        }).then(() => {\n          BasisModule.initializeBasis()\n        })\n      }\n\n      function transcodeLowLevel(taskConfig) {\n        const { basisFormat, width, height, hasAlpha } = taskConfig\n\n        const { transcoderFormat, engineFormat } = getTranscoderFormat(basisFormat, width, height, hasAlpha)\n\n        const blockByteLength = BasisModule.getBytesPerBlockOrPixel(transcoderFormat)\n\n        assert(BasisModule.isFormatSupported(transcoderFormat), 'THREE.BasisTextureLoader: Unsupported format.')\n\n        const mipmaps = []\n\n        if (basisFormat === BasisFormat.ETC1S) {\n          const transcoder = new BasisModule.LowLevelETC1SImageTranscoder()\n\n          const { endpointCount, endpointsData, selectorCount, selectorsData, tablesData } = taskConfig.globalData\n\n          try {\n            let ok\n\n            ok = transcoder.decodePalettes(endpointCount, endpointsData, selectorCount, selectorsData)\n\n            assert(ok, 'THREE.BasisTextureLoader: decodePalettes() failed.')\n\n            ok = transcoder.decodeTables(tablesData)\n\n            assert(ok, 'THREE.BasisTextureLoader: decodeTables() failed.')\n\n            for (let i = 0; i < taskConfig.levels.length; i++) {\n              const level = taskConfig.levels[i]\n              const imageDesc = taskConfig.globalData.imageDescs[i]\n\n              const dstByteLength = getTranscodedImageByteLength(transcoderFormat, level.width, level.height)\n              const dst = new Uint8Array(dstByteLength)\n\n              ok = transcoder.transcodeImage(\n                transcoderFormat,\n                dst,\n                dstByteLength / blockByteLength,\n                level.data,\n                getWidthInBlocks(transcoderFormat, level.width),\n                getHeightInBlocks(transcoderFormat, level.height),\n                level.width,\n                level.height,\n                level.index,\n                imageDesc.rgbSliceByteOffset,\n                imageDesc.rgbSliceByteLength,\n                imageDesc.alphaSliceByteOffset,\n                imageDesc.alphaSliceByteLength,\n                imageDesc.imageFlags,\n                hasAlpha,\n                false,\n                0,\n                0,\n              )\n\n              assert(ok, 'THREE.BasisTextureLoader: transcodeImage() failed for level ' + level.index + '.')\n\n              mipmaps.push({ data: dst, width: level.width, height: level.height })\n            }\n          } finally {\n            transcoder.delete()\n          }\n        } else {\n          for (let i = 0; i < taskConfig.levels.length; i++) {\n            const level = taskConfig.levels[i]\n\n            const dstByteLength = getTranscodedImageByteLength(transcoderFormat, level.width, level.height)\n            const dst = new Uint8Array(dstByteLength)\n\n            const ok = BasisModule.transcodeUASTCImage(\n              transcoderFormat,\n              dst,\n              dstByteLength / blockByteLength,\n              level.data,\n              getWidthInBlocks(transcoderFormat, level.width),\n              getHeightInBlocks(transcoderFormat, level.height),\n              level.width,\n              level.height,\n              level.index,\n              0,\n              level.data.byteLength,\n              0,\n              hasAlpha,\n              false,\n              0,\n              0,\n              -1,\n              -1,\n            )\n\n            assert(ok, 'THREE.BasisTextureLoader: transcodeUASTCImage() failed for level ' + level.index + '.')\n\n            mipmaps.push({ data: dst, width: level.width, height: level.height })\n          }\n        }\n\n        return { width, height, hasAlpha, mipmaps, format: engineFormat }\n      }\n\n      function transcode(buffer) {\n        const basisFile = new BasisModule.BasisFile(new Uint8Array(buffer))\n\n        const basisFormat = basisFile.isUASTC() ? BasisFormat.UASTC_4x4 : BasisFormat.ETC1S\n        const width = basisFile.getImageWidth(0, 0)\n        const height = basisFile.getImageHeight(0, 0)\n        const levels = basisFile.getNumLevels(0)\n        const hasAlpha = basisFile.getHasAlpha()\n\n        function cleanup() {\n          basisFile.close()\n          basisFile.delete()\n        }\n\n        const { transcoderFormat, engineFormat } = getTranscoderFormat(basisFormat, width, height, hasAlpha)\n\n        if (!width || !height || !levels) {\n          cleanup()\n          throw new Error('THREE.BasisTextureLoader:\tInvalid texture')\n        }\n\n        if (!basisFile.startTranscoding()) {\n          cleanup()\n          throw new Error('THREE.BasisTextureLoader: .startTranscoding failed')\n        }\n\n        const mipmaps = []\n\n        for (let mip = 0; mip < levels; mip++) {\n          const mipWidth = basisFile.getImageWidth(0, mip)\n          const mipHeight = basisFile.getImageHeight(0, mip)\n          const dst = new Uint8Array(basisFile.getImageTranscodedSizeInBytes(0, mip, transcoderFormat))\n\n          const status = basisFile.transcodeImage(dst, 0, mip, transcoderFormat, 0, hasAlpha)\n\n          if (!status) {\n            cleanup()\n            throw new Error('THREE.BasisTextureLoader: .transcodeImage failed.')\n          }\n\n          mipmaps.push({ data: dst, width: mipWidth, height: mipHeight })\n        }\n\n        cleanup()\n\n        return { width, height, hasAlpha, mipmaps, format: engineFormat }\n      }\n\n      //\n\n      // Optimal choice of a transcoder target format depends on the Basis format (ETC1S or UASTC),\n      // device capabilities, and texture dimensions. The list below ranks the formats separately\n      // for ETC1S and UASTC.\n      //\n      // In some cases, transcoding UASTC to RGBA32 might be preferred for higher quality (at\n      // significant memory cost) compared to ETC1/2, BC1/3, and PVRTC. The transcoder currently\n      // chooses RGBA32 only as a last resort and does not expose that option to the caller.\n      const FORMAT_OPTIONS = [\n        {\n          if: 'astcSupported',\n          basisFormat: [BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ASTC_4x4, TranscoderFormat.ASTC_4x4],\n          engineFormat: [EngineFormat.RGBA_ASTC_4x4_Format, EngineFormat.RGBA_ASTC_4x4_Format],\n          priorityETC1S: Infinity,\n          priorityUASTC: 1,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'bptcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC7_M5, TranscoderFormat.BC7_M5],\n          engineFormat: [EngineFormat.RGBA_BPTC_Format, EngineFormat.RGBA_BPTC_Format],\n          priorityETC1S: 3,\n          priorityUASTC: 2,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'dxtSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.BC1, TranscoderFormat.BC3],\n          engineFormat: [EngineFormat.RGB_S3TC_DXT1_Format, EngineFormat.RGBA_S3TC_DXT5_Format],\n          priorityETC1S: 4,\n          priorityUASTC: 5,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc2Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC2],\n          engineFormat: [EngineFormat.RGB_ETC2_Format, EngineFormat.RGBA_ETC2_EAC_Format],\n          priorityETC1S: 1,\n          priorityUASTC: 3,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'etc1Supported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.ETC1, TranscoderFormat.ETC1],\n          engineFormat: [EngineFormat.RGB_ETC1_Format, EngineFormat.RGB_ETC1_Format],\n          priorityETC1S: 2,\n          priorityUASTC: 4,\n          needsPowerOfTwo: false,\n        },\n        {\n          if: 'pvrtcSupported',\n          basisFormat: [BasisFormat.ETC1S, BasisFormat.UASTC_4x4],\n          transcoderFormat: [TranscoderFormat.PVRTC1_4_RGB, TranscoderFormat.PVRTC1_4_RGBA],\n          engineFormat: [EngineFormat.RGB_PVRTC_4BPPV1_Format, EngineFormat.RGBA_PVRTC_4BPPV1_Format],\n          priorityETC1S: 5,\n          priorityUASTC: 6,\n          needsPowerOfTwo: true,\n        },\n      ]\n\n      const ETC1S_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityETC1S - b.priorityETC1S\n      })\n      const UASTC_OPTIONS = FORMAT_OPTIONS.sort(function (a, b) {\n        return a.priorityUASTC - b.priorityUASTC\n      })\n\n      function getTranscoderFormat(basisFormat, width, height, hasAlpha) {\n        let transcoderFormat\n        let engineFormat\n\n        const options = basisFormat === BasisFormat.ETC1S ? ETC1S_OPTIONS : UASTC_OPTIONS\n\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i]\n\n          if (!config[opt.if]) continue\n          if (!opt.basisFormat.includes(basisFormat)) continue\n          if (opt.needsPowerOfTwo && !(isPowerOfTwo(width) && isPowerOfTwo(height))) continue\n\n          transcoderFormat = opt.transcoderFormat[hasAlpha ? 1 : 0]\n          engineFormat = opt.engineFormat[hasAlpha ? 1 : 0]\n\n          return { transcoderFormat, engineFormat }\n        }\n\n        console.warn('THREE.BasisTextureLoader: No suitable compressed texture format found. Decoding to RGBA32.')\n\n        transcoderFormat = TranscoderFormat.RGBA32\n        engineFormat = EngineFormat.RGBAFormat\n\n        return { transcoderFormat, engineFormat }\n      }\n\n      function assert(ok, message) {\n        if (!ok) throw new Error(message)\n      }\n\n      function getWidthInBlocks(transcoderFormat, width) {\n        return Math.ceil(width / BasisModule.getFormatBlockWidth(transcoderFormat))\n      }\n\n      function getHeightInBlocks(transcoderFormat, height) {\n        return Math.ceil(height / BasisModule.getFormatBlockHeight(transcoderFormat))\n      }\n\n      function getTranscodedImageByteLength(transcoderFormat, width, height) {\n        const blockByteLength = BasisModule.getBytesPerBlockOrPixel(transcoderFormat)\n\n        if (BasisModule.formatIsUncompressed(transcoderFormat)) {\n          return width * height * blockByteLength\n        }\n\n        if (transcoderFormat === TranscoderFormat.PVRTC1_4_RGB || transcoderFormat === TranscoderFormat.PVRTC1_4_RGBA) {\n          // GL requires extra padding for very small textures:\n          // https://www.khronos.org/registry/OpenGL/extensions/IMG/IMG_texture_compression_pvrtc.txt\n          const paddedWidth = (width + 3) & ~3\n          const paddedHeight = (height + 3) & ~3\n\n          return (Math.max(8, paddedWidth) * Math.max(8, paddedHeight) * 4 + 7) / 8\n        }\n\n        return getWidthInBlocks(transcoderFormat, width) * getHeightInBlocks(transcoderFormat, height) * blockByteLength\n      }\n\n      function isPowerOfTwo(value) {\n        if (value <= 2) return true\n\n        return (value & (value - 1)) === 0 && value !== 0\n      }\n    }\n\n    constructor(manager) {\n      super(manager)\n\n      this.transcoderPath = ''\n      this.transcoderBinary = null\n      this.transcoderPending = null\n\n      this.workerLimit = 4\n      this.workerPool = []\n      this.workerNextTaskID = 1\n      this.workerSourceURL = ''\n      this.workerConfig = null\n    }\n\n    setTranscoderPath(path) {\n      this.transcoderPath = path\n\n      return this\n    }\n\n    setWorkerLimit(workerLimit) {\n      this.workerLimit = workerLimit\n\n      return this\n    }\n\n    detectSupport(renderer) {\n      this.workerConfig = {\n        astcSupported: renderer.extensions.has('WEBGL_compressed_texture_astc'),\n        etc1Supported: renderer.extensions.has('WEBGL_compressed_texture_etc1'),\n        etc2Supported: renderer.extensions.has('WEBGL_compressed_texture_etc'),\n        dxtSupported: renderer.extensions.has('WEBGL_compressed_texture_s3tc'),\n        bptcSupported: renderer.extensions.has('EXT_texture_compression_bptc'),\n        pvrtcSupported:\n          renderer.extensions.has('WEBGL_compressed_texture_pvrtc') ||\n          renderer.extensions.has('WEBKIT_WEBGL_compressed_texture_pvrtc'),\n      }\n\n      return this\n    }\n\n    load(url, onLoad, onProgress, onError) {\n      const loader = new FileLoader(this.manager)\n\n      loader.setResponseType('arraybuffer')\n      loader.setWithCredentials(this.withCredentials)\n\n      const texture = new CompressedTexture()\n\n      loader.load(\n        url,\n        (buffer) => {\n          // Check for an existing task using this buffer. A transferred buffer cannot be transferred\n          // again from this thread.\n          if (_taskCache.has(buffer)) {\n            const cachedTask = _taskCache.get(buffer)\n\n            return cachedTask.promise.then(onLoad).catch(onError)\n          }\n\n          this._createTexture([buffer])\n            .then(function (_texture) {\n              texture.copy(_texture)\n              texture.needsUpdate = true\n\n              if (onLoad) onLoad(texture)\n            })\n            .catch(onError)\n        },\n        onProgress,\n        onError,\n      )\n\n      return texture\n    }\n\n    /** Low-level transcoding API, exposed for use by KTX2Loader. */\n    parseInternalAsync(options) {\n      const { levels } = options\n\n      const buffers = new Set()\n\n      for (let i = 0; i < levels.length; i++) {\n        buffers.add(levels[i].data.buffer)\n      }\n\n      return this._createTexture(Array.from(buffers), { ...options, lowLevel: true })\n    }\n\n    /**\n     * @param {ArrayBuffer[]} buffers\n     * @param {object?} config\n     * @return {Promise<CompressedTexture>}\n     */\n    _createTexture(buffers, config = {}) {\n      let worker\n      let taskID\n\n      const taskConfig = config\n      let taskCost = 0\n\n      for (let i = 0; i < buffers.length; i++) {\n        taskCost += buffers[i].byteLength\n      }\n\n      const texturePending = this._allocateWorker(taskCost)\n        .then((_worker) => {\n          worker = _worker\n          taskID = this.workerNextTaskID++\n\n          return new Promise((resolve, reject) => {\n            worker._callbacks[taskID] = { resolve, reject }\n\n            worker.postMessage({ type: 'transcode', id: taskID, buffers: buffers, taskConfig: taskConfig }, buffers)\n          })\n        })\n        .then((message) => {\n          const { mipmaps, width, height, format } = message\n\n          const texture = new CompressedTexture(mipmaps, width, height, format, UnsignedByteType)\n          texture.minFilter = mipmaps.length === 1 ? LinearFilter : LinearMipmapLinearFilter\n          texture.magFilter = LinearFilter\n          texture.generateMipmaps = false\n          texture.needsUpdate = true\n\n          return texture\n        })\n\n      // Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n      texturePending\n        .catch(() => true)\n        .then(() => {\n          if (worker && taskID) {\n            worker._taskLoad -= taskCost\n            delete worker._callbacks[taskID]\n          }\n        })\n\n      // Cache the task result.\n      _taskCache.set(buffers[0], { promise: texturePending })\n\n      return texturePending\n    }\n\n    _initTranscoder() {\n      if (!this.transcoderPending) {\n        // Load transcoder wrapper.\n        const jsLoader = new FileLoader(this.manager)\n        jsLoader.setPath(this.transcoderPath)\n        jsLoader.setWithCredentials(this.withCredentials)\n        const jsContent = new Promise((resolve, reject) => {\n          jsLoader.load('basis_transcoder.js', resolve, undefined, reject)\n        })\n\n        // Load transcoder WASM binary.\n        const binaryLoader = new FileLoader(this.manager)\n        binaryLoader.setPath(this.transcoderPath)\n        binaryLoader.setResponseType('arraybuffer')\n        binaryLoader.setWithCredentials(this.withCredentials)\n        const binaryContent = new Promise((resolve, reject) => {\n          binaryLoader.load('basis_transcoder.wasm', resolve, undefined, reject)\n        })\n\n        this.transcoderPending = Promise.all([jsContent, binaryContent]).then(([jsContent, binaryContent]) => {\n          const fn = BasisTextureLoader.BasisWorker.toString()\n\n          const body = [\n            '/* constants */',\n            'let _EngineFormat = ' + JSON.stringify(BasisTextureLoader.EngineFormat),\n            'let _TranscoderFormat = ' + JSON.stringify(BasisTextureLoader.TranscoderFormat),\n            'let _BasisFormat = ' + JSON.stringify(BasisTextureLoader.BasisFormat),\n            '/* basis_transcoder.js */',\n            jsContent,\n            '/* worker */',\n            fn.substring(fn.indexOf('{') + 1, fn.lastIndexOf('}')),\n          ].join('\\n')\n\n          this.workerSourceURL = URL.createObjectURL(new Blob([body]))\n          this.transcoderBinary = binaryContent\n        })\n      }\n\n      return this.transcoderPending\n    }\n\n    _allocateWorker(taskCost) {\n      return this._initTranscoder().then(() => {\n        if (this.workerPool.length < this.workerLimit) {\n          const worker = new Worker(this.workerSourceURL)\n\n          worker._callbacks = {}\n          worker._taskLoad = 0\n\n          worker.postMessage({\n            type: 'init',\n            config: this.workerConfig,\n            transcoderBinary: this.transcoderBinary,\n          })\n\n          worker.onmessage = function (e) {\n            const message = e.data\n\n            switch (message.type) {\n              case 'transcode':\n                worker._callbacks[message.id].resolve(message)\n                break\n\n              case 'error':\n                worker._callbacks[message.id].reject(message)\n                break\n\n              default:\n                console.error('THREE.BasisTextureLoader: Unexpected message, \"' + message.type + '\"')\n            }\n          }\n\n          this.workerPool.push(worker)\n        } else {\n          this.workerPool.sort(function (a, b) {\n            return a._taskLoad > b._taskLoad ? -1 : 1\n          })\n        }\n\n        const worker = this.workerPool[this.workerPool.length - 1]\n\n        worker._taskLoad += taskCost\n\n        return worker\n      })\n    }\n\n    dispose() {\n      for (let i = 0; i < this.workerPool.length; i++) {\n        this.workerPool[i].terminate()\n      }\n\n      this.workerPool.length = 0\n\n      return this\n    }\n  }\n\n  return BasisTextureLoader\n})()\n\nexport { BasisTextureLoader }\n"], "names": ["Loader", "<PERSON><PERSON><PERSON><PERSON>", "CompressedTexture", "UnsignedByteType", "LinearFilter", "LinearMipmapLinearFilter", "js<PERSON><PERSON><PERSON>", "binaryContent", "worker", "BasisTextureLoader", "RGBAFormat", "RGBA_ASTC_4x4_Format", "RGBA_BPTC_Format", "RGBA_ETC2_EAC_Format", "RGBA_PVRTC_4BPPV1_Format", "RGBA_S3TC_DXT5_Format", "RGB_ETC1_Format", "RGB_ETC2_Format", "RGB_PVRTC_4BPPV1_Format", "RGB_S3TC_DXT1_Format"], "mappings": ";;;;;;;;;AAgCA,MAAM,aAAa,oBAAI,QAAS;AAE3B,MAAC,qBAAsC,uBAAM;AAChD,QAAM,sBAAN,cAAiCA,MAAAA,OAAO;AAAA,IA8XtC,YAAY,SAAS;AACnB,YAAM,OAAO;AAEb,WAAK,iBAAiB;AACtB,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AAEzB,WAAK,cAAc;AACnB,WAAK,aAAa,CAAE;AACpB,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;AACvB,WAAK,eAAe;AAAA,IACrB;AAAA,IAED,kBAAkB,MAAM;AACtB,WAAK,iBAAiB;AAEtB,aAAO;AAAA,IACR;AAAA,IAED,eAAe,aAAa;AAC1B,WAAK,cAAc;AAEnB,aAAO;AAAA,IACR;AAAA,IAED,cAAc,UAAU;AACtB,WAAK,eAAe;AAAA,QAClB,eAAe,SAAS,WAAW,IAAI,+BAA+B;AAAA,QACtE,eAAe,SAAS,WAAW,IAAI,+BAA+B;AAAA,QACtE,eAAe,SAAS,WAAW,IAAI,8BAA8B;AAAA,QACrE,cAAc,SAAS,WAAW,IAAI,+BAA+B;AAAA,QACrE,eAAe,SAAS,WAAW,IAAI,8BAA8B;AAAA,QACrE,gBACE,SAAS,WAAW,IAAI,gCAAgC,KACxD,SAAS,WAAW,IAAI,uCAAuC;AAAA,MAClE;AAED,aAAO;AAAA,IACR;AAAA,IAED,KAAK,KAAK,QAAQ,YAAY,SAAS;AACrC,YAAM,SAAS,IAAIC,iBAAW,KAAK,OAAO;AAE1C,aAAO,gBAAgB,aAAa;AACpC,aAAO,mBAAmB,KAAK,eAAe;AAE9C,YAAM,UAAU,IAAIC,wBAAmB;AAEvC,aAAO;AAAA,QACL;AAAA,QACA,CAAC,WAAW;AAGV,cAAI,WAAW,IAAI,MAAM,GAAG;AAC1B,kBAAM,aAAa,WAAW,IAAI,MAAM;AAExC,mBAAO,WAAW,QAAQ,KAAK,MAAM,EAAE,MAAM,OAAO;AAAA,UACrD;AAED,eAAK,eAAe,CAAC,MAAM,CAAC,EACzB,KAAK,SAAU,UAAU;AACxB,oBAAQ,KAAK,QAAQ;AACrB,oBAAQ,cAAc;AAEtB,gBAAI;AAAQ,qBAAO,OAAO;AAAA,UACxC,CAAa,EACA,MAAM,OAAO;AAAA,QACjB;AAAA,QACD;AAAA,QACA;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAAA;AAAA,IAGD,mBAAmB,SAAS;AAC1B,YAAM,EAAE,OAAM,IAAK;AAEnB,YAAM,UAAU,oBAAI,IAAK;AAEzB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,gBAAQ,IAAI,OAAO,CAAC,EAAE,KAAK,MAAM;AAAA,MAClC;AAED,aAAO,KAAK,eAAe,MAAM,KAAK,OAAO,GAAG,EAAE,GAAG,SAAS,UAAU,MAAM;AAAA,IAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOD,eAAe,SAAS,SAAS,IAAI;AACnC,UAAI;AACJ,UAAI;AAEJ,YAAM,aAAa;AACnB,UAAI,WAAW;AAEf,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,oBAAY,QAAQ,CAAC,EAAE;AAAA,MACxB;AAED,YAAM,iBAAiB,KAAK,gBAAgB,QAAQ,EACjD,KAAK,CAAC,YAAY;AACjB,iBAAS;AACT,iBAAS,KAAK;AAEd,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,iBAAO,WAAW,MAAM,IAAI,EAAE,SAAS,OAAQ;AAE/C,iBAAO,YAAY,EAAE,MAAM,aAAa,IAAI,QAAQ,SAAkB,WAAsB,GAAI,OAAO;AAAA,QACnH,CAAW;AAAA,MACX,CAAS,EACA,KAAK,CAAC,YAAY;AACjB,cAAM,EAAE,SAAS,OAAO,QAAQ,OAAQ,IAAG;AAE3C,cAAM,UAAU,IAAIA,wBAAkB,SAAS,OAAO,QAAQ,QAAQC,sBAAgB;AACtF,gBAAQ,YAAY,QAAQ,WAAW,IAAIC,MAAY,eAAGC,MAAwB;AAClF,gBAAQ,YAAYD,MAAY;AAChC,gBAAQ,kBAAkB;AAC1B,gBAAQ,cAAc;AAEtB,eAAO;AAAA,MACjB,CAAS;AAGH,qBACG,MAAM,MAAM,IAAI,EAChB,KAAK,MAAM;AACV,YAAI,UAAU,QAAQ;AACpB,iBAAO,aAAa;AACpB,iBAAO,OAAO,WAAW,MAAM;AAAA,QAChC;AAAA,MACX,CAAS;AAGH,iBAAW,IAAI,QAAQ,CAAC,GAAG,EAAE,SAAS,gBAAgB;AAEtD,aAAO;AAAA,IACR;AAAA,IAED,kBAAkB;AAChB,UAAI,CAAC,KAAK,mBAAmB;AAE3B,cAAM,WAAW,IAAIH,iBAAW,KAAK,OAAO;AAC5C,iBAAS,QAAQ,KAAK,cAAc;AACpC,iBAAS,mBAAmB,KAAK,eAAe;AAChD,cAAM,YAAY,IAAI,QAAQ,CAAC,SAAS,WAAW;AACjD,mBAAS,KAAK,uBAAuB,SAAS,QAAW,MAAM;AAAA,QACzE,CAAS;AAGD,cAAM,eAAe,IAAIA,iBAAW,KAAK,OAAO;AAChD,qBAAa,QAAQ,KAAK,cAAc;AACxC,qBAAa,gBAAgB,aAAa;AAC1C,qBAAa,mBAAmB,KAAK,eAAe;AACpD,cAAM,gBAAgB,IAAI,QAAQ,CAAC,SAAS,WAAW;AACrD,uBAAa,KAAK,yBAAyB,SAAS,QAAW,MAAM;AAAA,QAC/E,CAAS;AAED,aAAK,oBAAoB,QAAQ,IAAI,CAAC,WAAW,aAAa,CAAC,EAAE,KAAK,CAAC,CAACK,YAAWC,cAAa,MAAM;AACpG,gBAAM,KAAK,oBAAmB,YAAY,SAAU;AAEpD,gBAAM,OAAO;AAAA,YACX;AAAA,YACA,yBAAyB,KAAK,UAAU,oBAAmB,YAAY;AAAA,YACvE,6BAA6B,KAAK,UAAU,oBAAmB,gBAAgB;AAAA,YAC/E,wBAAwB,KAAK,UAAU,oBAAmB,WAAW;AAAA,YACrE;AAAA,YACAD;AAAA,YACA;AAAA,YACA,GAAG,UAAU,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG,YAAY,GAAG,CAAC;AAAA,UACjE,EAAY,KAAK,IAAI;AAEX,eAAK,kBAAkB,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3D,eAAK,mBAAmBC;AAAA,QAClC,CAAS;AAAA,MACF;AAED,aAAO,KAAK;AAAA,IACb;AAAA,IAED,gBAAgB,UAAU;AACxB,aAAO,KAAK,kBAAkB,KAAK,MAAM;AACvC,YAAI,KAAK,WAAW,SAAS,KAAK,aAAa;AAC7C,gBAAMC,UAAS,IAAI,OAAO,KAAK,eAAe;AAE9C,UAAAA,QAAO,aAAa,CAAE;AACtB,UAAAA,QAAO,YAAY;AAEnB,UAAAA,QAAO,YAAY;AAAA,YACjB,MAAM;AAAA,YACN,QAAQ,KAAK;AAAA,YACb,kBAAkB,KAAK;AAAA,UACnC,CAAW;AAED,UAAAA,QAAO,YAAY,SAAU,GAAG;AAC9B,kBAAM,UAAU,EAAE;AAElB,oBAAQ,QAAQ,MAAI;AAAA,cAClB,KAAK;AACH,gBAAAA,QAAO,WAAW,QAAQ,EAAE,EAAE,QAAQ,OAAO;AAC7C;AAAA,cAEF,KAAK;AACH,gBAAAA,QAAO,WAAW,QAAQ,EAAE,EAAE,OAAO,OAAO;AAC5C;AAAA,cAEF;AACE,wBAAQ,MAAM,oDAAoD,QAAQ,OAAO,GAAG;AAAA,YACvF;AAAA,UACF;AAED,eAAK,WAAW,KAAKA,OAAM;AAAA,QACrC,OAAe;AACL,eAAK,WAAW,KAAK,SAAU,GAAG,GAAG;AACnC,mBAAO,EAAE,YAAY,EAAE,YAAY,KAAK;AAAA,UACpD,CAAW;AAAA,QACF;AAED,cAAM,SAAS,KAAK,WAAW,KAAK,WAAW,SAAS,CAAC;AAEzD,eAAO,aAAa;AAEpB,eAAO;AAAA,MACf,CAAO;AAAA,IACF;AAAA,IAED,UAAU;AACR,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,aAAK,WAAW,CAAC,EAAE,UAAW;AAAA,MAC/B;AAED,WAAK,WAAW,SAAS;AAEzB,aAAO;AAAA,IACR;AAAA,EACF;AA9mBD,MAAMC,sBAAN;AAGE;AAAA,gBAHIA,qBAGG,eAAc;AAAA,IACnB,OAAO;AAAA,IACP,WAAW;AAAA,EACZ;AAED,gBARIA,qBAQG,oBAAmB;AAAA,IACxB,MAAM;AAAA,IACN,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,oBAAoB;AAAA,IACpB,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,6BAA6B;AAAA,IAC7B,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,UAAU;AAAA,EACX;AAED,gBA5BIA,qBA4BG,gBAAe;AAAA,IACpB,YAAYC,MAAU;AAAA,IACtB,sBAAsBC,MAAoB;AAAA,IAC1C,kBAAkBC,MAAgB;AAAA,IAClC,sBAAsBC,MAAoB;AAAA,IAC1C,0BAA0BC,MAAwB;AAAA,IAClD,uBAAuBC,MAAqB;AAAA,IAC5C,iBAAiBC,MAAe;AAAA,IAChC,iBAAiBC,MAAe;AAAA,IAChC,yBAAyBC,MAAuB;AAAA,IAChD,sBAAsBC,MAAoB;AAAA,EAC3C;AAID;AAAA,gBA3CIV,qBA2CG,eAAc,WAAY;AAC/B,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,UAAM,eAAe;AACrB,UAAM,mBAAmB;AACzB,UAAM,cAAc;AAEpB,gBAAY,SAAU,GAAG;AACvB,YAAM,UAAU,EAAE;AAElB,cAAQ,QAAQ,MAAI;AAAA,QAClB,KAAK;AACH,mBAAS,QAAQ;AACjB,eAAK,QAAQ,gBAAgB;AAC7B;AAAA,QAEF,KAAK;AACH,4BAAkB,KAAK,MAAM;AAC3B,gBAAI;AACF,oBAAM,EAAE,OAAO,QAAQ,UAAU,SAAS,OAAQ,IAAG,QAAQ,WAAW,WACpE,kBAAkB,QAAQ,UAAU,IACpC,UAAU,QAAQ,QAAQ,CAAC,CAAC;AAEhC,oBAAM,UAAU,CAAE;AAElB,uBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,EAAE,GAAG;AACvC,wBAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK,MAAM;AAAA,cACpC;AAED,mBAAK;AAAA,gBACH,EAAE,MAAM,aAAa,IAAI,QAAQ,IAAI,OAAO,QAAQ,UAAU,SAAS,OAAQ;AAAA,gBAC/E;AAAA,cACD;AAAA,YACF,SAAQ,OAAP;AACA,sBAAQ,MAAM,KAAK;AAEnB,mBAAK,YAAY,EAAE,MAAM,SAAS,IAAI,QAAQ,IAAI,OAAO,MAAM,QAAO,CAAE;AAAA,YACzE;AAAA,UACf,CAAa;AACD;AAAA,MACH;AAAA,IACF;AAED,aAAS,KAAK,YAAY;AACxB,0BAAoB,IAAI,QAAQ,CAAC,YAAY;AAC3C,sBAAc,EAAE,YAAY,sBAAsB,QAAS;AAC3D,cAAM,WAAW;AAAA,MAC3B,CAAS,EAAE,KAAK,MAAM;AACZ,oBAAY,gBAAiB;AAAA,MACvC,CAAS;AAAA,IACF;AAED,aAAS,kBAAkB,YAAY;AACrC,YAAM,EAAE,aAAa,OAAO,QAAQ,SAAU,IAAG;AAEjD,YAAM,EAAE,kBAAkB,iBAAiB,oBAAoB,aAAa,OAAO,QAAQ,QAAQ;AAEnG,YAAM,kBAAkB,YAAY,wBAAwB,gBAAgB;AAE5E,aAAO,YAAY,kBAAkB,gBAAgB,GAAG,+CAA+C;AAEvG,YAAM,UAAU,CAAE;AAElB,UAAI,gBAAgB,YAAY,OAAO;AACrC,cAAM,aAAa,IAAI,YAAY,6BAA8B;AAEjE,cAAM,EAAE,eAAe,eAAe,eAAe,eAAe,WAAU,IAAK,WAAW;AAE9F,YAAI;AACF,cAAI;AAEJ,eAAK,WAAW,eAAe,eAAe,eAAe,eAAe,aAAa;AAEzF,iBAAO,IAAI,oDAAoD;AAE/D,eAAK,WAAW,aAAa,UAAU;AAEvC,iBAAO,IAAI,kDAAkD;AAE7D,mBAAS,IAAI,GAAG,IAAI,WAAW,OAAO,QAAQ,KAAK;AACjD,kBAAM,QAAQ,WAAW,OAAO,CAAC;AACjC,kBAAM,YAAY,WAAW,WAAW,WAAW,CAAC;AAEpD,kBAAM,gBAAgB,6BAA6B,kBAAkB,MAAM,OAAO,MAAM,MAAM;AAC9F,kBAAM,MAAM,IAAI,WAAW,aAAa;AAExC,iBAAK,WAAW;AAAA,cACd;AAAA,cACA;AAAA,cACA,gBAAgB;AAAA,cAChB,MAAM;AAAA,cACN,iBAAiB,kBAAkB,MAAM,KAAK;AAAA,cAC9C,kBAAkB,kBAAkB,MAAM,MAAM;AAAA,cAChD,MAAM;AAAA,cACN,MAAM;AAAA,cACN,MAAM;AAAA,cACN,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV,UAAU;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACD;AAED,mBAAO,IAAI,iEAAiE,MAAM,QAAQ,GAAG;AAE7F,oBAAQ,KAAK,EAAE,MAAM,KAAK,OAAO,MAAM,OAAO,QAAQ,MAAM,OAAM,CAAE;AAAA,UACrE;AAAA,QACb,UAAoB;AACR,qBAAW,OAAQ;AAAA,QACpB;AAAA,MACX,OAAe;AACL,iBAAS,IAAI,GAAG,IAAI,WAAW,OAAO,QAAQ,KAAK;AACjD,gBAAM,QAAQ,WAAW,OAAO,CAAC;AAEjC,gBAAM,gBAAgB,6BAA6B,kBAAkB,MAAM,OAAO,MAAM,MAAM;AAC9F,gBAAM,MAAM,IAAI,WAAW,aAAa;AAExC,gBAAM,KAAK,YAAY;AAAA,YACrB;AAAA,YACA;AAAA,YACA,gBAAgB;AAAA,YAChB,MAAM;AAAA,YACN,iBAAiB,kBAAkB,MAAM,KAAK;AAAA,YAC9C,kBAAkB,kBAAkB,MAAM,MAAM;AAAA,YAChD,MAAM;AAAA,YACN,MAAM;AAAA,YACN,MAAM;AAAA,YACN;AAAA,YACA,MAAM,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACD;AAED,iBAAO,IAAI,sEAAsE,MAAM,QAAQ,GAAG;AAElG,kBAAQ,KAAK,EAAE,MAAM,KAAK,OAAO,MAAM,OAAO,QAAQ,MAAM,OAAM,CAAE;AAAA,QACrE;AAAA,MACF;AAED,aAAO,EAAE,OAAO,QAAQ,UAAU,SAAS,QAAQ,aAAc;AAAA,IAClE;AAED,aAAS,UAAU,QAAQ;AACzB,YAAM,YAAY,IAAI,YAAY,UAAU,IAAI,WAAW,MAAM,CAAC;AAElE,YAAM,cAAc,UAAU,QAAO,IAAK,YAAY,YAAY,YAAY;AAC9E,YAAM,QAAQ,UAAU,cAAc,GAAG,CAAC;AAC1C,YAAM,SAAS,UAAU,eAAe,GAAG,CAAC;AAC5C,YAAM,SAAS,UAAU,aAAa,CAAC;AACvC,YAAM,WAAW,UAAU,YAAa;AAExC,eAAS,UAAU;AACjB,kBAAU,MAAO;AACjB,kBAAU,OAAQ;AAAA,MACnB;AAED,YAAM,EAAE,kBAAkB,iBAAiB,oBAAoB,aAAa,OAAO,QAAQ,QAAQ;AAEnG,UAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ;AAChC,gBAAS;AACT,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC5D;AAED,UAAI,CAAC,UAAU,oBAAoB;AACjC,gBAAS;AACT,cAAM,IAAI,MAAM,oDAAoD;AAAA,MACrE;AAED,YAAM,UAAU,CAAE;AAElB,eAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACrC,cAAM,WAAW,UAAU,cAAc,GAAG,GAAG;AAC/C,cAAM,YAAY,UAAU,eAAe,GAAG,GAAG;AACjD,cAAM,MAAM,IAAI,WAAW,UAAU,8BAA8B,GAAG,KAAK,gBAAgB,CAAC;AAE5F,cAAM,SAAS,UAAU,eAAe,KAAK,GAAG,KAAK,kBAAkB,GAAG,QAAQ;AAElF,YAAI,CAAC,QAAQ;AACX,kBAAS;AACT,gBAAM,IAAI,MAAM,mDAAmD;AAAA,QACpE;AAED,gBAAQ,KAAK,EAAE,MAAM,KAAK,OAAO,UAAU,QAAQ,WAAW;AAAA,MAC/D;AAED,cAAS;AAET,aAAO,EAAE,OAAO,QAAQ,UAAU,SAAS,QAAQ,aAAc;AAAA,IAClE;AAWD,UAAM,iBAAiB;AAAA,MACrB;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,SAAS;AAAA,QACnC,kBAAkB,CAAC,iBAAiB,UAAU,iBAAiB,QAAQ;AAAA,QACvE,cAAc,CAAC,aAAa,sBAAsB,aAAa,oBAAoB;AAAA,QACnF,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,QAAQ,iBAAiB,MAAM;AAAA,QACnE,cAAc,CAAC,aAAa,kBAAkB,aAAa,gBAAgB;AAAA,QAC3E,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,KAAK,iBAAiB,GAAG;AAAA,QAC7D,cAAc,CAAC,aAAa,sBAAsB,aAAa,qBAAqB;AAAA,QACpF,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,MAAM,iBAAiB,IAAI;AAAA,QAC/D,cAAc,CAAC,aAAa,iBAAiB,aAAa,oBAAoB;AAAA,QAC9E,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,MAAM,iBAAiB,IAAI;AAAA,QAC/D,cAAc,CAAC,aAAa,iBAAiB,aAAa,eAAe;AAAA,QACzE,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,MACD;AAAA,QACE,IAAI;AAAA,QACJ,aAAa,CAAC,YAAY,OAAO,YAAY,SAAS;AAAA,QACtD,kBAAkB,CAAC,iBAAiB,cAAc,iBAAiB,aAAa;AAAA,QAChF,cAAc,CAAC,aAAa,yBAAyB,aAAa,wBAAwB;AAAA,QAC1F,eAAe;AAAA,QACf,eAAe;AAAA,QACf,iBAAiB;AAAA,MAClB;AAAA,IACF;AAED,UAAM,gBAAgB,eAAe,KAAK,SAAU,GAAG,GAAG;AACxD,aAAO,EAAE,gBAAgB,EAAE;AAAA,IACnC,CAAO;AACD,UAAM,gBAAgB,eAAe,KAAK,SAAU,GAAG,GAAG;AACxD,aAAO,EAAE,gBAAgB,EAAE;AAAA,IACnC,CAAO;AAED,aAAS,oBAAoB,aAAa,OAAO,QAAQ,UAAU;AACjE,UAAI;AACJ,UAAI;AAEJ,YAAM,UAAU,gBAAgB,YAAY,QAAQ,gBAAgB;AAEpE,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAM,MAAM,QAAQ,CAAC;AAErB,YAAI,CAAC,OAAO,IAAI,EAAE;AAAG;AACrB,YAAI,CAAC,IAAI,YAAY,SAAS,WAAW;AAAG;AAC5C,YAAI,IAAI,mBAAmB,EAAE,aAAa,KAAK,KAAK,aAAa,MAAM;AAAI;AAE3E,2BAAmB,IAAI,iBAAiB,WAAW,IAAI,CAAC;AACxD,uBAAe,IAAI,aAAa,WAAW,IAAI,CAAC;AAEhD,eAAO,EAAE,kBAAkB,aAAc;AAAA,MAC1C;AAED,cAAQ,KAAK,4FAA4F;AAEzG,yBAAmB,iBAAiB;AACpC,qBAAe,aAAa;AAE5B,aAAO,EAAE,kBAAkB,aAAc;AAAA,IAC1C;AAED,aAAS,OAAO,IAAI,SAAS;AAC3B,UAAI,CAAC;AAAI,cAAM,IAAI,MAAM,OAAO;AAAA,IACjC;AAED,aAAS,iBAAiB,kBAAkB,OAAO;AACjD,aAAO,KAAK,KAAK,QAAQ,YAAY,oBAAoB,gBAAgB,CAAC;AAAA,IAC3E;AAED,aAAS,kBAAkB,kBAAkB,QAAQ;AACnD,aAAO,KAAK,KAAK,SAAS,YAAY,qBAAqB,gBAAgB,CAAC;AAAA,IAC7E;AAED,aAAS,6BAA6B,kBAAkB,OAAO,QAAQ;AACrE,YAAM,kBAAkB,YAAY,wBAAwB,gBAAgB;AAE5E,UAAI,YAAY,qBAAqB,gBAAgB,GAAG;AACtD,eAAO,QAAQ,SAAS;AAAA,MACzB;AAED,UAAI,qBAAqB,iBAAiB,gBAAgB,qBAAqB,iBAAiB,eAAe;AAG7G,cAAM,cAAe,QAAQ,IAAK,CAAC;AACnC,cAAM,eAAgB,SAAS,IAAK,CAAC;AAErC,gBAAQ,KAAK,IAAI,GAAG,WAAW,IAAI,KAAK,IAAI,GAAG,YAAY,IAAI,IAAI,KAAK;AAAA,MACzE;AAED,aAAO,iBAAiB,kBAAkB,KAAK,IAAI,kBAAkB,kBAAkB,MAAM,IAAI;AAAA,IAClG;AAED,aAAS,aAAa,OAAO;AAC3B,UAAI,SAAS;AAAG,eAAO;AAEvB,cAAQ,QAAS,QAAQ,OAAQ,KAAK,UAAU;AAAA,IACjD;AAAA,EACF;AAoPH,SAAOA;AACT,GAAC;;"}