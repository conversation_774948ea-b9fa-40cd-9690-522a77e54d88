import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const navigate = useNavigate()

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const data = await response.json()

      if (response.ok) {
        // حفظ بيانات المستخدم والتوكن
        localStorage.setItem('user', JSON.stringify(data.user))
        localStorage.setItem('token', data.token)
        
        // إعادة توجيه حسب نوع المستخدم
        if (data.user.role === 'admin') {
          navigate('/admin')
        } else {
          navigate('/')
        }
      } else {
        setError(data.message || 'حدث خطأ في تسجيل الدخول')
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="auth-container">
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-6 col-lg-5">
            <div className="auth-card card shadow-lg border-0">
              <div className="card-body p-5">
                <div className="text-center mb-4">
                  <div className="auth-logo mb-3">
                    <i className="fas fa-shoe-prints fa-3x text-primary"></i>
                  </div>
                  <h2 className="fw-bold text-primary">تسجيل الدخول</h2>
                  <p className="text-muted">مرحباً بعودتك إلى FiftyFive</p>
                </div>

                {error && (
                  <div className="alert alert-danger" role="alert">
                    <i className="fas fa-exclamation-triangle me-2"></i>
                    {error}
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label fw-bold">
                      <i className="fas fa-envelope me-2"></i>
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      className="form-control form-control-lg"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="أدخل بريدك الإلكتروني"
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="password" className="form-label fw-bold">
                      <i className="fas fa-lock me-2"></i>
                      كلمة المرور
                    </label>
                    <input
                      type="password"
                      className="form-control form-control-lg"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="أدخل كلمة المرور"
                      required
                    />
                  </div>

                  <div className="d-flex justify-content-between align-items-center mb-4">
                    <div className="form-check">
                      <input className="form-check-input" type="checkbox" id="remember" />
                      <label className="form-check-label" htmlFor="remember">
                        تذكرني
                      </label>
                    </div>
                    <Link to="/forgot-password" className="text-decoration-none">
                      نسيت كلمة المرور؟
                    </Link>
                  </div>

                  <div className="d-grid mb-3">
                    <button 
                      type="submit" 
                      className="btn btn-primary btn-lg fw-bold"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                          جاري تسجيل الدخول...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-sign-in-alt me-2"></i>
                          تسجيل الدخول
                        </>
                      )}
                    </button>
                  </div>
                </form>

                <div className="text-center">
                  <p className="mb-0">
                    ليس لديك حساب؟ 
                    <Link to="/register" className="text-decoration-none fw-bold ms-1">
                      إنشاء حساب جديد
                    </Link>
                  </p>
                </div>

                <hr className="my-4" />

                <div className="social-login">
                  <p className="text-center text-muted mb-3">أو سجل الدخول باستخدام</p>
                  <div className="row g-2">
                    <div className="col-6">
                      <button className="btn btn-outline-danger w-100">
                        <i className="fab fa-google me-2"></i>
                        Google
                      </button>
                    </div>
                    <div className="col-6">
                      <button className="btn btn-outline-primary w-100">
                        <i className="fab fa-facebook me-2"></i>
                        Facebook
                      </button>
                    </div>
                  </div>
                </div>

                {/* Demo Accounts */}
                <div className="demo-accounts mt-4 p-3 bg-light rounded">
                  <h6 className="fw-bold mb-2">
                    <i className="fas fa-info-circle me-2 text-info"></i>
                    حسابات تجريبية
                  </h6>
                  <div className="row">
                    <div className="col-6">
                      <small className="text-muted d-block">مستخدم عادي:</small>
                      <small className="fw-bold"><EMAIL></small><br />
                      <small className="fw-bold">123456</small>
                    </div>
                    <div className="col-6">
                      <small className="text-muted d-block">مدير:</small>
                      <small className="fw-bold"><EMAIL></small><br />
                      <small className="fw-bold">123456</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
