import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'

const Cart = () => {
  const [cartItems, setCartItems] = useState([])
  const [total, setTotal] = useState(0)
  const navigate = useNavigate()

  useEffect(() => {
    // تحميل عناصر السلة من localStorage
    const cart = JSON.parse(localStorage.getItem('cart') || '[]')
    setCartItems(cart)
    calculateTotal(cart)
  }, [])

  const calculateTotal = (items) => {
    const totalAmount = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    setTotal(totalAmount)
  }

  const updateQuantity = (id, newQuantity) => {
    if (newQuantity <= 0) {
      removeItem(id)
      return
    }

    const updatedCart = cartItems.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    )
    setCartItems(updatedCart)
    localStorage.setItem('cart', JSON.stringify(updatedCart))
    calculateTotal(updatedCart)
  }

  const removeItem = (id) => {
    const updatedCart = cartItems.filter(item => item.id !== id)
    setCartItems(updatedCart)
    localStorage.setItem('cart', JSON.stringify(updatedCart))
    calculateTotal(updatedCart)
  }

  const clearCart = () => {
    setCartItems([])
    localStorage.removeItem('cart')
    setTotal(0)
  }

  const proceedToCheckout = () => {
    if (cartItems.length === 0) {
      alert('السلة فارغة!')
      return
    }
    navigate('/checkout')
  }

  if (cartItems.length === 0) {
    return (
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-6 text-center">
            <div className="empty-cart-container p-5">
              <i className="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
              <h2 className="mb-3">السلة فارغة</h2>
              <p className="text-muted mb-4">لم تقم بإضافة أي منتجات إلى السلة بعد</p>
              <Link to="/" className="btn btn-primary btn-lg">
                <i className="fas fa-shopping-bag me-2"></i>
                ابدأ التسوق
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-4">
      <div className="row">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h1 className="fw-bold">
              <i className="fas fa-shopping-cart me-2 text-primary"></i>
              سلة التسوق
            </h1>
            <button 
              className="btn btn-outline-danger"
              onClick={clearCart}
            >
              <i className="fas fa-trash me-2"></i>
              إفراغ السلة
            </button>
          </div>
        </div>
      </div>

      <div className="row">
        <div className="col-lg-8">
          <div className="cart-items">
            {cartItems.map((item) => (
              <div key={`${item.id}-${item.color}-${item.size}`} className="cart-item card mb-3 shadow-sm">
                <div className="card-body">
                  <div className="row align-items-center">
                    <div className="col-md-2">
                      <img 
                        src={item.image || '/images/placeholder-shoe.jpg'} 
                        alt={item.name}
                        className="img-fluid rounded"
                        style={{ height: '80px', objectFit: 'cover' }}
                      />
                    </div>
                    <div className="col-md-4">
                      <h5 className="fw-bold mb-1">{item.name}</h5>
                      <p className="text-muted mb-1">
                        <small>
                          <i className="fas fa-palette me-1"></i>
                          اللون: {item.color}
                        </small>
                      </p>
                      <p className="text-muted mb-0">
                        <small>
                          <i className="fas fa-ruler me-1"></i>
                          المقاس: {item.size}
                        </small>
                      </p>
                    </div>
                    <div className="col-md-2">
                      <div className="quantity-controls d-flex align-items-center">
                        <button 
                          className="btn btn-outline-secondary btn-sm"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        >
                          <i className="fas fa-minus"></i>
                        </button>
                        <span className="mx-3 fw-bold">{item.quantity}</span>
                        <button 
                          className="btn btn-outline-secondary btn-sm"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        >
                          <i className="fas fa-plus"></i>
                        </button>
                      </div>
                    </div>
                    <div className="col-md-2 text-center">
                      <p className="fw-bold text-primary mb-0">
                        {item.price} جنيه
                      </p>
                    </div>
                    <div className="col-md-2 text-center">
                      <p className="fw-bold text-success mb-2">
                        {item.price * item.quantity} جنيه
                      </p>
                      <button 
                        className="btn btn-outline-danger btn-sm"
                        onClick={() => removeItem(item.id)}
                      >
                        <i className="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="col-lg-4">
          <div className="cart-summary card shadow-sm sticky-top">
            <div className="card-header bg-primary text-white">
              <h5 className="mb-0">
                <i className="fas fa-calculator me-2"></i>
                ملخص الطلب
              </h5>
            </div>
            <div className="card-body">
              <div className="summary-row d-flex justify-content-between mb-2">
                <span>عدد المنتجات:</span>
                <span className="fw-bold">{cartItems.length}</span>
              </div>
              <div className="summary-row d-flex justify-content-between mb-2">
                <span>إجمالي الكمية:</span>
                <span className="fw-bold">
                  {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
                </span>
              </div>
              <div className="summary-row d-flex justify-content-between mb-2">
                <span>المجموع الفرعي:</span>
                <span className="fw-bold">{total} جنيه</span>
              </div>
              <div className="summary-row d-flex justify-content-between mb-2">
                <span>الشحن:</span>
                <span className="text-success fw-bold">مجاني</span>
              </div>
              <hr />
              <div className="summary-row d-flex justify-content-between mb-3">
                <span className="fs-5 fw-bold">الإجمالي:</span>
                <span className="fs-5 fw-bold text-primary">{total} جنيه</span>
              </div>
              
              <div className="d-grid gap-2">
                <button 
                  className="btn btn-primary btn-lg fw-bold"
                  onClick={proceedToCheckout}
                >
                  <i className="fas fa-credit-card me-2"></i>
                  إتمام الشراء
                </button>
                <Link to="/" className="btn btn-outline-primary">
                  <i className="fas fa-arrow-right me-2"></i>
                  متابعة التسوق
                </Link>
              </div>
            </div>
          </div>

          {/* Promo Code Section */}
          <div className="card shadow-sm mt-3">
            <div className="card-body">
              <h6 className="fw-bold mb-3">
                <i className="fas fa-tag me-2 text-warning"></i>
                كود الخصم
              </h6>
              <div className="input-group">
                <input 
                  type="text" 
                  className="form-control" 
                  placeholder="أدخل كود الخصم"
                />
                <button className="btn btn-outline-warning">
                  تطبيق
                </button>
              </div>
            </div>
          </div>

          {/* Security Info */}
          <div className="card shadow-sm mt-3">
            <div className="card-body text-center">
              <i className="fas fa-shield-alt fa-2x text-success mb-2"></i>
              <h6 className="fw-bold">تسوق آمن</h6>
              <p className="small text-muted mb-0">
                جميع المعاملات محمية بتشفير SSL
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Cart
