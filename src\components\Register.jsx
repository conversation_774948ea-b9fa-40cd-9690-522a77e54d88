import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

const Register = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    address: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const navigate = useNavigate()

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // التحقق من تطابق كلمات المرور
    if (formData.password !== formData.confirmPassword) {
      setError('كلمات المرور غير متطابقة')
      setLoading(false)
      return
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          phone: formData.phone,
          address: formData.address
        })
      })

      const data = await response.json()

      if (response.ok) {
        // حفظ بيانات المستخدم والتوكن
        localStorage.setItem('user', JSON.stringify(data.user))
        localStorage.setItem('token', data.token)
        
        // إعادة توجيه للصفحة الرئيسية
        navigate('/')
      } else {
        setError(data.message || 'حدث خطأ في إنشاء الحساب')
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="auth-container">
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-8 col-lg-6">
            <div className="auth-card card shadow-lg border-0">
              <div className="card-body p-5">
                <div className="text-center mb-4">
                  <div className="auth-logo mb-3">
                    <i className="fas fa-user-plus fa-3x text-success"></i>
                  </div>
                  <h2 className="fw-bold text-success">إنشاء حساب جديد</h2>
                  <p className="text-muted">انضم إلى عائلة FiftyFive</p>
                </div>

                {error && (
                  <div className="alert alert-danger" role="alert">
                    <i className="fas fa-exclamation-triangle me-2"></i>
                    {error}
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="name" className="form-label fw-bold">
                        <i className="fas fa-user me-2"></i>
                        الاسم الكامل
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="أدخل اسمك الكامل"
                        required
                      />
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="email" className="form-label fw-bold">
                        <i className="fas fa-envelope me-2"></i>
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        className="form-control"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="أدخل بريدك الإلكتروني"
                        required
                      />
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label htmlFor="password" className="form-label fw-bold">
                        <i className="fas fa-lock me-2"></i>
                        كلمة المرور
                      </label>
                      <input
                        type="password"
                        className="form-control"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="أدخل كلمة المرور"
                        minLength="6"
                        required
                      />
                    </div>

                    <div className="col-md-6 mb-3">
                      <label htmlFor="confirmPassword" className="form-label fw-bold">
                        <i className="fas fa-lock me-2"></i>
                        تأكيد كلمة المرور
                      </label>
                      <input
                        type="password"
                        className="form-control"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        placeholder="أعد إدخال كلمة المرور"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-3">
                    <label htmlFor="phone" className="form-label fw-bold">
                      <i className="fas fa-phone me-2"></i>
                      رقم الهاتف
                    </label>
                    <input
                      type="tel"
                      className="form-control"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="أدخل رقم هاتفك"
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="address" className="form-label fw-bold">
                      <i className="fas fa-map-marker-alt me-2"></i>
                      العنوان
                    </label>
                    <textarea
                      className="form-control"
                      id="address"
                      name="address"
                      value={formData.address}
                      onChange={handleChange}
                      placeholder="أدخل عنوانك الكامل"
                      rows="3"
                      required
                    ></textarea>
                  </div>

                  <div className="form-check mb-4">
                    <input className="form-check-input" type="checkbox" id="terms" required />
                    <label className="form-check-label" htmlFor="terms">
                      أوافق على 
                      <Link to="/terms" className="text-decoration-none ms-1">الشروط والأحكام</Link>
                      و
                      <Link to="/privacy" className="text-decoration-none ms-1">سياسة الخصوصية</Link>
                    </label>
                  </div>

                  <div className="d-grid mb-3">
                    <button 
                      type="submit" 
                      className="btn btn-success btn-lg fw-bold"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                          جاري إنشاء الحساب...
                        </>
                      ) : (
                        <>
                          <i className="fas fa-user-plus me-2"></i>
                          إنشاء الحساب
                        </>
                      )}
                    </button>
                  </div>
                </form>

                <div className="text-center">
                  <p className="mb-0">
                    لديك حساب بالفعل؟ 
                    <Link to="/login" className="text-decoration-none fw-bold ms-1">
                      تسجيل الدخول
                    </Link>
                  </p>
                </div>

                <hr className="my-4" />

                <div className="benefits-section">
                  <h6 className="fw-bold text-center mb-3">
                    <i className="fas fa-star me-2 text-warning"></i>
                    مميزات العضوية
                  </h6>
                  <div className="row text-center">
                    <div className="col-4">
                      <i className="fas fa-shipping-fast fa-2x text-primary mb-2"></i>
                      <p className="small mb-0">شحن مجاني</p>
                    </div>
                    <div className="col-4">
                      <i className="fas fa-percent fa-2x text-success mb-2"></i>
                      <p className="small mb-0">خصومات حصرية</p>
                    </div>
                    <div className="col-4">
                      <i className="fas fa-gift fa-2x text-warning mb-2"></i>
                      <p className="small mb-0">عروض خاصة</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Register
