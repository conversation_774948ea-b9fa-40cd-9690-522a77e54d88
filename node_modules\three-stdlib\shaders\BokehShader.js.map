{"version": 3, "file": "BokehShader.js", "sources": ["../../src/shaders/BokehShader.ts"], "sourcesContent": ["/**\n * Depth-of-field shader with bokeh\n * ported from GLSL shader by <PERSON>\n * http://artmartinsh.blogspot.com/2010/02/glsl-lens-blur-filter-with-bokeh.html\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type BokehShaderDefines = {\n  DEPTH_PACKING: number\n  PERSPECTIVE_CAMERA: number\n}\n\nexport type BokehShaderUniforms = {\n  aperture: IUniform<number>\n  aspect: IUniform<number>\n  farClip: IUniform<number>\n  focus: IUniform<number>\n  maxblur: IUniform<number>\n  nearClip: IUniform<number>\n  tColor: IUniform<Texture | null>\n  tDepth: IUniform<Texture | null>\n}\n\nexport interface IBokehShader extends IShader<BokehShaderUniforms, BokehShaderDefines> {}\n\nexport const BokehShader: IBokehShader = {\n  defines: {\n    DEPTH_PACKING: 1,\n    PERSPECTIVE_CAMERA: 1,\n  },\n\n  uniforms: {\n    tColor: { value: null },\n    tDepth: { value: null },\n    focus: { value: 1.0 },\n    aspect: { value: 1.0 },\n    aperture: { value: 0.025 },\n    maxblur: { value: 0.01 },\n    nearClip: { value: 1.0 },\n    farClip: { value: 1000.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    #include <common>\n\n    varying vec2 vUv;\n\n    uniform sampler2D tColor;\n    uniform sampler2D tDepth;\n\n    uniform float maxblur; // max blur amount\n    uniform float aperture; // aperture - bigger values for shallower depth of field\n\n    uniform float nearClip;\n    uniform float farClip;\n\n    uniform float focus;\n    uniform float aspect;\n\n    #include <packing>\n\n    float getDepth( const in vec2 screenPosition ) {\n    \t#if DEPTH_PACKING == 1\n    \treturn unpackRGBAToDepth( texture2D( tDepth, screenPosition ) );\n    \t#else\n    \treturn texture2D( tDepth, screenPosition ).x;\n    \t#endif\n    }\n\n    float getViewZ( const in float depth ) {\n    \t#if PERSPECTIVE_CAMERA == 1\n    \treturn perspectiveDepthToViewZ( depth, nearClip, farClip );\n    \t#else\n    \treturn orthographicDepthToViewZ( depth, nearClip, farClip );\n    \t#endif\n    }\n\n    void main() {\n\n    \tvec2 aspectcorrect = vec2( 1.0, aspect );\n\n    \tfloat viewZ = getViewZ( getDepth( vUv ) );\n\n    \tfloat factor = ( focus + viewZ ); // viewZ is <= 0, so this is a difference equation\n\n    \tvec2 dofblur = vec2 ( clamp( factor * aperture, -maxblur, maxblur ) );\n\n    \tvec2 dofblur9 = dofblur * 0.9;\n    \tvec2 dofblur7 = dofblur * 0.7;\n    \tvec2 dofblur4 = dofblur * 0.4;\n\n    \tvec4 col = vec4( 0.0 );\n\n    \tcol += texture2D( tColor, vUv.xy );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.0,   0.4  ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.15,  0.37 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.29,  0.29 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.37,  0.15 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.40,  0.0  ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.37, -0.15 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.29, -0.29 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.15, -0.37 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.0,  -0.4  ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.15,  0.37 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.29,  0.29 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.37,  0.15 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.4,   0.0  ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.37, -0.15 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.29, -0.29 ) * aspectcorrect ) * dofblur );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.15, -0.37 ) * aspectcorrect ) * dofblur );\n\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.15,  0.37 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.37,  0.15 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.37, -0.15 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.15, -0.37 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.15,  0.37 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.37,  0.15 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.37, -0.15 ) * aspectcorrect ) * dofblur9 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.15, -0.37 ) * aspectcorrect ) * dofblur9 );\n\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.29,  0.29 ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.40,  0.0  ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.29, -0.29 ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.0,  -0.4  ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.29,  0.29 ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.4,   0.0  ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.29, -0.29 ) * aspectcorrect ) * dofblur7 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.0,   0.4  ) * aspectcorrect ) * dofblur7 );\n\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.29,  0.29 ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.4,   0.0  ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.29, -0.29 ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.0,  -0.4  ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.29,  0.29 ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.4,   0.0  ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2( -0.29, -0.29 ) * aspectcorrect ) * dofblur4 );\n    \tcol += texture2D( tColor, vUv.xy + ( vec2(  0.0,   0.4  ) * aspectcorrect ) * dofblur4 );\n\n    \tgl_FragColor = col / 41.0;\n    \tgl_FragColor.a = 1.0;\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AA2BO,MAAM,cAA4B;AAAA,EACvC,SAAS;AAAA,IACP,eAAe;AAAA,IACf,oBAAoB;AAAA,EACtB;AAAA,EAEA,UAAU;AAAA,IACR,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,QAAQ,EAAE,OAAO,KAAK;AAAA,IACtB,OAAO,EAAE,OAAO,EAAI;AAAA,IACpB,QAAQ,EAAE,OAAO,EAAI;AAAA,IACrB,UAAU,EAAE,OAAO,MAAM;AAAA,IACzB,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,UAAU,EAAE,OAAO,EAAI;AAAA,IACvB,SAAS,EAAE,OAAO,IAAO;AAAA,EAC3B;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqG7B;"}