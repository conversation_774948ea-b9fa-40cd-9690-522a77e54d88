import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

const Checkout = () => {
  const [cartItems, setCartItems] = useState([])
  const [total, setTotal] = useState(0)
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState('card')
  const [shippingInfo, setShippingInfo] = useState({
    fullName: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
    notes: ''
  })
  const navigate = useNavigate()

  useEffect(() => {
    // تحميل بيانات السلة والمستخدم
    const cart = JSON.parse(localStorage.getItem('cart') || '[]')
    const userData = JSON.parse(localStorage.getItem('user') || 'null')
    
    if (cart.length === 0) {
      navigate('/cart')
      return
    }

    setCartItems(cart)
    setUser(userData)
    
    // حساب الإجمالي
    const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    setTotal(totalAmount)

    // تعبئة بيانات الشحن من بيانات المستخدم
    if (userData) {
      setShippingInfo({
        fullName: userData.name || '',
        phone: userData.phone || '',
        address: userData.address || '',
        city: '',
        postalCode: '',
        notes: ''
      })
    }
  }, [navigate])

  const handleShippingChange = (e) => {
    setShippingInfo({
      ...shippingInfo,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmitOrder = async (e) => {
    e.preventDefault()
    setLoading(true)

    try {
      const orderData = {
        items: cartItems,
        total: total,
        shippingInfo: shippingInfo,
        paymentMethod: paymentMethod,
        userId: user?.id
      }

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(orderData)
      })

      const data = await response.json()

      if (response.ok) {
        // مسح السلة
        localStorage.removeItem('cart')
        
        // إعادة توجيه لصفحة تأكيد الطلب
        navigate(`/order-success/${data.orderId}`)
      } else {
        alert(data.message || 'حدث خطأ في إتمام الطلب')
      }
    } catch (error) {
      alert('حدث خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container py-4">
      <div className="row">
        <div className="col-12 mb-4">
          <h1 className="fw-bold">
            <i className="fas fa-credit-card me-2 text-primary"></i>
            إتمام الشراء
          </h1>
          <nav aria-label="breadcrumb">
            <ol className="breadcrumb">
              <li className="breadcrumb-item"><a href="/">الرئيسية</a></li>
              <li className="breadcrumb-item"><a href="/cart">السلة</a></li>
              <li className="breadcrumb-item active">الدفع</li>
            </ol>
          </nav>
        </div>
      </div>

      <form onSubmit={handleSubmitOrder}>
        <div className="row">
          <div className="col-lg-8">
            {/* معلومات الشحن */}
            <div className="card shadow-sm mb-4">
              <div className="card-header bg-primary text-white">
                <h5 className="mb-0">
                  <i className="fas fa-shipping-fast me-2"></i>
                  معلومات الشحن
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label fw-bold">الاسم الكامل</label>
                    <input
                      type="text"
                      className="form-control"
                      name="fullName"
                      value={shippingInfo.fullName}
                      onChange={handleShippingChange}
                      required
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label fw-bold">رقم الهاتف</label>
                    <input
                      type="tel"
                      className="form-control"
                      name="phone"
                      value={shippingInfo.phone}
                      onChange={handleShippingChange}
                      required
                    />
                  </div>
                </div>
                <div className="mb-3">
                  <label className="form-label fw-bold">العنوان التفصيلي</label>
                  <textarea
                    className="form-control"
                    name="address"
                    value={shippingInfo.address}
                    onChange={handleShippingChange}
                    rows="3"
                    required
                  ></textarea>
                </div>
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label fw-bold">المدينة</label>
                    <select
                      className="form-select"
                      name="city"
                      value={shippingInfo.city}
                      onChange={handleShippingChange}
                      required
                    >
                      <option value="">اختر المدينة</option>
                      <option value="الرياض">الرياض</option>
                      <option value="جدة">جدة</option>
                      <option value="الدمام">الدمام</option>
                      <option value="مكة">مكة المكرمة</option>
                      <option value="المدينة">المدينة المنورة</option>
                      <option value="الطائف">الطائف</option>
                      <option value="تبوك">تبوك</option>
                      <option value="أخرى">أخرى</option>
                    </select>
                  </div>
                  <div className="col-md-6 mb-3">
                    <label className="form-label fw-bold">الرمز البريدي</label>
                    <input
                      type="text"
                      className="form-control"
                      name="postalCode"
                      value={shippingInfo.postalCode}
                      onChange={handleShippingChange}
                    />
                  </div>
                </div>
                <div className="mb-3">
                  <label className="form-label fw-bold">ملاحظات إضافية</label>
                  <textarea
                    className="form-control"
                    name="notes"
                    value={shippingInfo.notes}
                    onChange={handleShippingChange}
                    rows="2"
                    placeholder="أي ملاحظات خاصة بالتوصيل..."
                  ></textarea>
                </div>
              </div>
            </div>

            {/* طريقة الدفع */}
            <div className="card shadow-sm mb-4">
              <div className="card-header bg-success text-white">
                <h5 className="mb-0">
                  <i className="fas fa-credit-card me-2"></i>
                  طريقة الدفع
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <div className="form-check payment-option">
                      <input
                        className="form-check-input"
                        type="radio"
                        name="paymentMethod"
                        id="card"
                        value="card"
                        checked={paymentMethod === 'card'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                      />
                      <label className="form-check-label" htmlFor="card">
                        <i className="fas fa-credit-card me-2 text-primary"></i>
                        بطاقة ائتمانية
                      </label>
                    </div>
                  </div>
                  <div className="col-md-6 mb-3">
                    <div className="form-check payment-option">
                      <input
                        className="form-check-input"
                        type="radio"
                        name="paymentMethod"
                        id="cash"
                        value="cash"
                        checked={paymentMethod === 'cash'}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                      />
                      <label className="form-check-label" htmlFor="cash">
                        <i className="fas fa-money-bill-wave me-2 text-success"></i>
                        الدفع عند الاستلام
                      </label>
                    </div>
                  </div>
                </div>

                {paymentMethod === 'card' && (
                  <div className="card-payment-form mt-3 p-3 bg-light rounded">
                    <div className="row">
                      <div className="col-md-8 mb-3">
                        <label className="form-label">رقم البطاقة</label>
                        <input
                          type="text"
                          className="form-control"
                          placeholder="1234 5678 9012 3456"
                          maxLength="19"
                        />
                      </div>
                      <div className="col-md-4 mb-3">
                        <label className="form-label">CVV</label>
                        <input
                          type="text"
                          className="form-control"
                          placeholder="123"
                          maxLength="3"
                        />
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-6 mb-3">
                        <label className="form-label">تاريخ الانتهاء</label>
                        <input
                          type="text"
                          className="form-control"
                          placeholder="MM/YY"
                          maxLength="5"
                        />
                      </div>
                      <div className="col-md-6 mb-3">
                        <label className="form-label">اسم حامل البطاقة</label>
                        <input
                          type="text"
                          className="form-control"
                          placeholder="الاسم كما هو مكتوب على البطاقة"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="col-lg-4">
            {/* ملخص الطلب */}
            <div className="card shadow-sm sticky-top">
              <div className="card-header bg-warning text-dark">
                <h5 className="mb-0">
                  <i className="fas fa-receipt me-2"></i>
                  ملخص الطلب
                </h5>
              </div>
              <div className="card-body">
                {cartItems.map((item) => (
                  <div key={`${item.id}-${item.color}-${item.size}`} className="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                    <div>
                      <h6 className="mb-0">{item.name}</h6>
                      <small className="text-muted">
                        {item.color} - مقاس {item.size} × {item.quantity}
                      </small>
                    </div>
                    <span className="fw-bold">{item.price * item.quantity} ر.س</span>
                  </div>
                ))}
                
                <div className="d-flex justify-content-between mb-2">
                  <span>المجموع الفرعي:</span>
                  <span>{total} ر.س</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>الشحن:</span>
                  <span className="text-success">مجاني</span>
                </div>
                <div className="d-flex justify-content-between mb-2">
                  <span>الضريبة (15%):</span>
                  <span>{Math.round(total * 0.15)} ر.س</span>
                </div>
                <hr />
                <div className="d-flex justify-content-between mb-3">
                  <span className="fs-5 fw-bold">الإجمالي:</span>
                  <span className="fs-5 fw-bold text-primary">
                    {Math.round(total + (total * 0.15))} ر.س
                  </span>
                </div>

                <div className="d-grid">
                  <button 
                    type="submit" 
                    className="btn btn-primary btn-lg fw-bold"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        جاري المعالجة...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-check me-2"></i>
                        تأكيد الطلب
                      </>
                    )}
                  </button>
                </div>

                <div className="text-center mt-3">
                  <small className="text-muted">
                    <i className="fas fa-shield-alt me-1"></i>
                    معاملة آمنة ومحمية
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  )
}

export default Checkout
