import express from "express";
import mongoose from "mongoose";
import cors from "cors";
import path from "path";
import dotenv from "dotenv";

dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static("public"));

// MongoDB Connection
mongoose.connect(
  process.env.MONGODB_URI || "mongodb://localhost:27017/shoe-store",
  {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  }
);

const db = mongoose.connection;
db.on("error", console.error.bind(console, "MongoDB connection error:"));
db.once("open", () => {
  console.log("Connected to MongoDB");
});

// Shoe Schema
const shoeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  colors: [
    {
      type: String,
      required: true,
    },
  ],
  sizes: [
    {
      type: Number,
      required: true,
    },
  ],
  description: {
    type: String,
    required: true,
  },
  features: [
    {
      type: String,
    },
  ],
  images: [
    {
      type: String,
    },
  ],
  position: {
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 },
  },
  category: {
    type: String,
    default: "رياضي",
  },
  brand: {
    type: String,
    required: true,
  },
  inStock: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

const Shoe = mongoose.model("Shoe", shoeSchema);

// Routes

// Get all shoes
app.get("/api/shoes", async (req, res) => {
  try {
    const shoes = await Shoe.find({ inStock: true });
    res.json(shoes);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get single shoe
app.get("/api/shoes/:id", async (req, res) => {
  try {
    const shoe = await Shoe.findById(req.params.id);
    if (!shoe) {
      return res.status(404).json({ message: "Shoe not found" });
    }
    res.json(shoe);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Create new shoe (Admin only)
app.post("/api/shoes", async (req, res) => {
  try {
    const shoe = new Shoe(req.body);
    const savedShoe = await shoe.save();
    res.status(201).json(savedShoe);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Update shoe
app.put("/api/shoes/:id", async (req, res) => {
  try {
    const shoe = await Shoe.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });
    if (!shoe) {
      return res.status(404).json({ message: "Shoe not found" });
    }
    res.json(shoe);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Delete shoe
app.delete("/api/shoes/:id", async (req, res) => {
  try {
    const shoe = await Shoe.findByIdAndDelete(req.params.id);
    if (!shoe) {
      return res.status(404).json({ message: "Shoe not found" });
    }
    res.json({ message: "Shoe deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Search shoes
app.get("/api/shoes/search/:query", async (req, res) => {
  try {
    const query = req.params.query;
    const shoes = await Shoe.find({
      $and: [
        { inStock: true },
        {
          $or: [
            { name: { $regex: query, $options: "i" } },
            { brand: { $regex: query, $options: "i" } },
            { category: { $regex: query, $options: "i" } },
          ],
        },
      ],
    });
    res.json(shoes);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Initialize sample data
app.post("/api/init-data", async (req, res) => {
  try {
    // Check if data already exists
    const existingShoes = await Shoe.countDocuments();
    if (existingShoes > 0) {
      return res.json({ message: "Data already exists" });
    }

    const sampleShoes = [
      {
        name: "Nike Air Max 270",
        price: 1200,
        colors: ["أسود", "أبيض", "أحمر"],
        sizes: [40, 41, 42, 43, 44],
        description: "كوتشي رياضي مريح مع تقنية Air Max للراحة القصوى",
        features: [
          "تقنية Air Max",
          "خامات عالية الجودة",
          "تصميم عصري",
          "مقاوم للماء",
        ],
        brand: "Nike",
        category: "رياضي",
        position: { x: -2, y: 1, z: 0 },
      },
      {
        name: "Adidas Ultraboost",
        price: 1500,
        colors: ["أزرق", "رمادي", "أخضر"],
        sizes: [39, 40, 41, 42, 43],
        description: "كوتشي للجري مع تقنية Boost للطاقة والراحة",
        features: ["تقنية Boost", "نعل مرن", "تهوية ممتازة", "خفيف الوزن"],
        brand: "Adidas",
        category: "جري",
        position: { x: 0, y: 1, z: 0 },
      },
      {
        name: "Puma RS-X",
        price: 900,
        colors: ["أصفر", "بنفسجي", "وردي"],
        sizes: [38, 39, 40, 41, 42],
        description: "كوتشي كاجوال بتصميم جريء وألوان زاهية",
        features: ["تصميم جريء", "ألوان متنوعة", "راحة يومية", "مناسب للشباب"],
        brand: "Puma",
        category: "كاجوال",
        position: { x: 2, y: 1, z: 0 },
      },
    ];

    await Shoe.insertMany(sampleShoes);
    res.json({ message: "Sample data created successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error(error.stack);
  res.status(500).json({ message: "Something went wrong!" });
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
